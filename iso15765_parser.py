#!/usr/bin/env python3
"""
ISO 15765-2 Transcript Parser

Main application for parsing CAN transcript files and decoding ISO-TP messages
according to the ISO 15765-2 standard.

Usage:
    python iso15765_parser.py [transcript_file]

If no file is specified, it will look for 'transcript.txt' in the current directory.
"""

import sys
import argparse
from typing import List, Optional
from can_frame_parser import CANFrameParser, CANFrame
from iso_tp_decoder import ISOTPDecoder, ISOTPMessage, ISOTPFrame, FrameType


class TranscriptParser:
    """Main parser for ISO 15765-2 transcript files"""

    def __init__(self, verbose: bool = False):
        self.can_parser = CANFrameParser()
        self.iso_decoder = ISOTPDecoder()
        self.verbose = verbose

        # Statistics
        self.stats = {
            'total_frames': 0,
            'single_frames': 0,
            'first_frames': 0,
            'consecutive_frames': 0,
            'flow_control_frames': 0,
            'complete_messages': 0,
            'parse_errors': 0
        }

    def parse_file(self, filename: str) -> List[ISOTPMessage]:
        """
        Parse a transcript file and return all complete ISO-TP messages

        Args:
            filename: Path to transcript file

        Returns:
            List of complete ISO-TP messages
        """
        print(f"Parsing transcript file: {filename}")
        print("=" * 60)

        try:
            # Parse CAN frames
            can_frames = self.can_parser.parse_file(filename)
            self.stats['total_frames'] = len(can_frames)

            if not can_frames:
                print("No CAN frames found in file")
                return []

            print(f"Found {len(can_frames)} CAN frames")
            print()

            # Process frames and decode ISO-TP messages
            messages = []

            for i, can_frame in enumerate(can_frames, 1):
                try:
                    # Process frame
                    message = self.iso_decoder.process_frame(can_frame)

                    # Decode frame for statistics and verbose output
                    iso_frame = self.iso_decoder.decode_frame(can_frame)
                    self._update_stats(iso_frame)

                    if self.verbose:
                        print(f"Frame {i:2d}: {self._format_frame_verbose(can_frame, iso_frame)}")

                    if message:
                        messages.append(message)
                        print(f"Message {len(messages):2d}: {self._format_message(message)}")
                        if self.verbose:
                            print()

                except Exception as e:
                    self.stats['parse_errors'] += 1
                    print(f"Error processing frame {i}: {e}")
                    if self.verbose:
                        print(f"  Raw frame: {can_frame}")
                        print()

            self.stats['complete_messages'] = len(messages)
            return messages

        except FileNotFoundError:
            print(f"Error: File '{filename}' not found")
            return []
        except Exception as e:
            print(f"Error reading file: {e}")
            return []

    def _update_stats(self, iso_frame: ISOTPFrame):
        """Update statistics based on frame type"""
        if iso_frame.frame_type == FrameType.SINGLE_FRAME:
            self.stats['single_frames'] += 1
        elif iso_frame.frame_type == FrameType.FIRST_FRAME:
            self.stats['first_frames'] += 1
        elif iso_frame.frame_type == FrameType.CONSECUTIVE_FRAME:
            self.stats['consecutive_frames'] += 1
        elif iso_frame.frame_type == FrameType.FLOW_CONTROL:
            self.stats['flow_control_frames'] += 1

    def _format_frame_verbose(self, can_frame: CANFrame, iso_frame: ISOTPFrame) -> str:
        """Format frame for verbose output"""
        raw_hex = ' '.join(f'{b:02X}' for b in can_frame.data_bytes)

        if iso_frame.frame_type == FrameType.SINGLE_FRAME:
            data_hex = ' '.join(f'{b:02X}' for b in iso_frame.sf_data)
            return f"CAN 0x{can_frame.can_id:03X} [{raw_hex}] -> SF(len={iso_frame.sf_length}): {data_hex}"

        elif iso_frame.frame_type == FrameType.FIRST_FRAME:
            data_hex = ' '.join(f'{b:02X}' for b in iso_frame.ff_data)
            return f"CAN 0x{can_frame.can_id:03X} [{raw_hex}] -> FF(total={iso_frame.ff_length}): {data_hex}..."

        elif iso_frame.frame_type == FrameType.CONSECUTIVE_FRAME:
            data_hex = ' '.join(f'{b:02X}' for b in iso_frame.cf_data)
            return f"CAN 0x{can_frame.can_id:03X} [{raw_hex}] -> CF(seq={iso_frame.cf_sequence}): {data_hex}"

        elif iso_frame.frame_type == FrameType.FLOW_CONTROL:
            return f"CAN 0x{can_frame.can_id:03X} [{raw_hex}] -> FC(status={iso_frame.fc_status.name}, BS={iso_frame.fc_block_size}, STmin={iso_frame.fc_separation_time})"

        return f"CAN 0x{can_frame.can_id:03X} [{raw_hex}] -> Unknown"

    def _format_message(self, message: ISOTPMessage) -> str:
        """Format complete message for output"""
        data_hex = ' '.join(f'{b:02X}' for b in message.data)
        return f"CAN ID 0x{message.can_id:03X} ({message.frame_count} frames, {len(message.data)} bytes): {data_hex}"

    def print_statistics(self):
        """Print parsing statistics"""
        print()
        print("Parsing Statistics:")
        print("-" * 30)
        print(f"Total CAN frames:      {self.stats['total_frames']}")
        print(f"Single frames:         {self.stats['single_frames']}")
        print(f"First frames:          {self.stats['first_frames']}")
        print(f"Consecutive frames:    {self.stats['consecutive_frames']}")
        print(f"Flow control frames:   {self.stats['flow_control_frames']}")
        print(f"Complete messages:     {self.stats['complete_messages']}")
        print(f"Parse errors:          {self.stats['parse_errors']}")

        if self.stats['total_frames'] > 0:
            efficiency = (self.stats['complete_messages'] / self.stats['total_frames']) * 100
            print(f"Message efficiency:    {efficiency:.1f}%")


def main():
    """Main entry point"""
    parser = argparse.ArgumentParser(
        description="Parse ISO 15765-2 transcript files and decode diagnostic messages",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  python iso15765_parser.py                    # Parse transcript.txt
  python iso15765_parser.py data.txt           # Parse data.txt
  python iso15765_parser.py -v transcript.txt  # Verbose output
        """
    )

    parser.add_argument(
        'filename',
        nargs='?',
        default='transcript.txt',
        help='Transcript file to parse (default: transcript.txt)'
    )

    parser.add_argument(
        '-v', '--verbose',
        action='store_true',
        help='Enable verbose output showing all frames'
    )

    parser.add_argument(
        '-s', '--stats',
        action='store_true',
        help='Show parsing statistics'
    )

    args = parser.parse_args()

    # Create parser and process file
    transcript_parser = TranscriptParser(verbose=args.verbose)
    messages = transcript_parser.parse_file(args.filename)

    # Show statistics if requested
    if args.stats or args.verbose:
        transcript_parser.print_statistics()

    # Summary
    print()
    print(f"Successfully decoded {len(messages)} ISO-TP messages from {args.filename}")

    return 0 if messages else 1


if __name__ == "__main__":
    sys.exit(main())