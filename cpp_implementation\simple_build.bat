@echo off
REM Simple build script for ISO 15765-2 Parser using g++ directly
REM This script compiles the C++ code without CMake dependency

echo Building ISO 15765-2 Parser with g++...
echo.

REM Create build directory
if not exist build mkdir build
if not exist build\bin mkdir build\bin

REM Copy transcript file
copy ..\transcript.txt build\bin\transcript.txt >nul

REM Compile with g++ (C++14 compatible)
echo Compiling source files...
g++ -std=c++14 -Wall -Wextra -O2 -o build\bin\iso15765_parser.exe ^
    can_frame_parser.cpp ^
    iso_tp_decoder.cpp ^
    transcript_parser.cpp ^
    main.cpp

if errorlevel 1 (
    echo.
    echo Build failed!
    exit /b 1
)

echo.
echo Build successful!
echo.

REM Run the parser
echo Running parser with transcript.txt...
cd build\bin
iso15765_parser.exe transcript.txt > ..\..\parsed.txt 2>&1
set EXIT_CODE=%errorlevel%

cd ..\..

if %EXIT_CODE% equ 0 (
    echo.
    echo SUCCESS: <PERSON><PERSON><PERSON> completed successfully
    echo Output saved to: parsed.txt
    echo.
    echo First few lines of output:
    echo -------------------------
    type parsed.txt | more +1 | head -5 2>nul || powershell "Get-Content 'parsed.txt' | Select-Object -First 5"
) else (
    echo.
    echo ERROR: Parser failed with exit code %EXIT_CODE%
    echo Error output:
    echo -------------
    type parsed.txt
    exit /b %EXIT_CODE%
)

echo.
echo Build and run completed.