#pragma once

#include <vector>
#include <string>
#include <stdexcept>
#include <cstdint>

namespace iso15765 {

/**
 * @brief Represents a single CAN frame
 */
class CANFrame {
public:
    /**
     * @brief Construct a CAN frame
     * @param can_id CAN identifier (11-bit)
     * @param data_bytes Data payload bytes
     */
    CANFrame(uint16_t can_id, const std::vector<uint8_t>& data_bytes);

    /**
     * @brief Get CAN ID
     * @return CAN identifier
     */
    uint16_t getCanId() const { return can_id_; }

    /**
     * @brief Get data bytes
     * @return Reference to data bytes vector
     */
    const std::vector<uint8_t>& getDataBytes() const { return data_bytes_; }

    /**
     * @brief Get data length code (DLC)
     * @return Number of data bytes
     */
    size_t getDLC() const { return data_bytes_.size(); }

    /**
     * @brief Convert frame to string representation
     * @return String representation of the frame
     */
    std::string toString() const;

private:
    uint16_t can_id_;
    std::vector<uint8_t> data_bytes_;
};

/**
 * @brief Parser for CAN frames from transcript format
 */
class CANFrameParser {
public:
    /**
     * @brief Parse a single line from transcript file
     * @param line Line in format "XXXDDDDDDDDDDDDDDDD" (19 chars)
     * @return Parsed CAN frame
     * @throws std::invalid_argument if line format is invalid
     */
    CANFrame parseLine(const std::string& line);

    /**
     * @brief Parse all CAN frames from a transcript file
     * @param filename Path to transcript file
     * @return Vector of parsed CAN frames
     * @throws std::runtime_error if file cannot be read
     */
    std::vector<CANFrame> parseFile(const std::string& filename);

private:
    /**
     * @brief Convert hex string to integer
     * @param hex_str Hexadecimal string
     * @return Integer value
     * @throws std::invalid_argument if string is not valid hex
     */
    uint32_t hexToInt(const std::string& hex_str);

    /**
     * @brief Convert hex character to integer
     * @param hex_char Hexadecimal character
     * @return Integer value (0-15)
     * @throws std::invalid_argument if character is not valid hex
     */
    uint8_t hexCharToInt(char hex_char);
};

} // namespace iso15765