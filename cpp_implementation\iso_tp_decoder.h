#pragma once

#include "can_frame_parser.h"
#include <vector>
#include <string>
#include <memory>
#include <unordered_map>
#include <cstdint>

// Simple optional-like class for C++14 compatibility
template<typename T>
class Optional {
public:
    Optional() : has_value_(false) {}
    Optional(const T& value) : has_value_(true), value_(value) {}

    bool has_value() const { return has_value_; }
    operator bool() const { return has_value_; }

    const T& operator*() const { return value_; }
    const T* operator->() const { return &value_; }

    void reset() { has_value_ = false; }
    void emplace(const T& value) { value_ = value; has_value_ = true; }

private:
    bool has_value_;
    T value_;
};

namespace iso15765 {

/**
 * @brief ISO-TP frame types according to ISO 15765-2
 */
enum class FrameType : uint8_t {
    SINGLE_FRAME = 0x0,
    FIRST_FRAME = 0x1,
    CONSECUTIVE_FRAME = 0x2,
    FLOW_CONTROL = 0x3
};

/**
 * @brief Flow control status values
 */
enum class FlowStatus : uint8_t {
    CONTINUE_TO_SEND = 0x0,
    WAIT = 0x1,
    OVERFLOW_ABORT = 0x2
};

/**
 * @brief Represents a decoded ISO-TP frame
 */
class ISOTPFrame {
public:
    /**
     * @brief Construct an ISO-TP frame
     * @param frame_type Type of ISO-TP frame
     * @param can_id CAN identifier
     * @param raw_data Raw CAN frame data
     */
    ISOTPFrame(FrameType frame_type, uint16_t can_id, const std::vector<uint8_t>& raw_data);

    // Getters
    FrameType getFrameType() const { return frame_type_; }
    uint16_t getCanId() const { return can_id_; }
    const std::vector<uint8_t>& getRawData() const { return raw_data_; }

    // Single Frame fields
    Optional<uint8_t> getSFLength() const { return sf_length_; }
    Optional<std::vector<uint8_t>> getSFData() const { return sf_data_; }

    // First Frame fields
    Optional<uint16_t> getFFLength() const { return ff_length_; }
    Optional<std::vector<uint8_t>> getFFData() const { return ff_data_; }

    // Consecutive Frame fields
    Optional<uint8_t> getCFSequence() const { return cf_sequence_; }
    Optional<std::vector<uint8_t>> getCFData() const { return cf_data_; }

    // Flow Control fields
    Optional<FlowStatus> getFCStatus() const { return fc_status_; }
    Optional<uint8_t> getFCBlockSize() const { return fc_block_size_; }
    Optional<uint8_t> getFCSeparationTime() const { return fc_separation_time_; }

    // Setters for frame-specific data
    void setSingleFrameData(uint8_t length, const std::vector<uint8_t>& data);
    void setFirstFrameData(uint16_t length, const std::vector<uint8_t>& data);
    void setConsecutiveFrameData(uint8_t sequence, const std::vector<uint8_t>& data);
    void setFlowControlData(FlowStatus status, uint8_t block_size, uint8_t separation_time);

    /**
     * @brief Convert frame to string representation
     * @return String representation
     */
    std::string toString() const;

private:
    FrameType frame_type_;
    uint16_t can_id_;
    std::vector<uint8_t> raw_data_;

    // Single Frame fields
    Optional<uint8_t> sf_length_;
    Optional<std::vector<uint8_t>> sf_data_;

    // First Frame fields
    Optional<uint16_t> ff_length_;
    Optional<std::vector<uint8_t>> ff_data_;

    // Consecutive Frame fields
    Optional<uint8_t> cf_sequence_;
    Optional<std::vector<uint8_t>> cf_data_;

    // Flow Control fields
    Optional<FlowStatus> fc_status_;
    Optional<uint8_t> fc_block_size_;
    Optional<uint8_t> fc_separation_time_;
};

/**
 * @brief Represents a complete ISO-TP message
 */
class ISOTPMessage {
public:
    /**
     * @brief Construct an ISO-TP message
     * @param can_id CAN identifier
     * @param data Message data
     * @param frame_count Number of frames used
     */
    ISOTPMessage(uint16_t can_id, const std::vector<uint8_t>& data, size_t frame_count);

    // Getters
    uint16_t getCanId() const { return can_id_; }
    const std::vector<uint8_t>& getData() const { return data_; }
    size_t getFrameCount() const { return frame_count_; }

    /**
     * @brief Convert message to string representation
     * @return String representation
     */
    std::string toString() const;

private:
    uint16_t can_id_;
    std::vector<uint8_t> data_;
    size_t frame_count_;
};

/**
 * @brief Message state for multi-frame reassembly
 */
struct MessageState {
    uint16_t total_length;
    std::vector<uint8_t> received_data;
    uint8_t expected_sequence;
    size_t frame_count;
};

/**
 * @brief ISO-TP protocol decoder
 */
class ISOTPDecoder {
public:
    /**
     * @brief Decode a CAN frame into an ISO-TP frame
     * @param can_frame CAN frame to decode
     * @return Decoded ISO-TP frame
     * @throws std::invalid_argument if frame format is invalid
     */
    ISOTPFrame decodeFrame(const CANFrame& can_frame);

    /**
     * @brief Process a CAN frame and return complete message if available
     * @param can_frame CAN frame to process
     * @return Complete message if available, nullptr if still assembling
     * @throws std::invalid_argument if frame format is invalid or protocol error
     */
    std::unique_ptr<ISOTPMessage> processFrame(const CANFrame& can_frame);

private:
    std::unordered_map<uint16_t, MessageState> pending_messages_;

    void decodeSingleFrame(ISOTPFrame& iso_frame, const std::vector<uint8_t>& data);
    void decodeFirstFrame(ISOTPFrame& iso_frame, const std::vector<uint8_t>& data);
    void decodeConsecutiveFrame(ISOTPFrame& iso_frame, const std::vector<uint8_t>& data);
    void decodeFlowControlFrame(ISOTPFrame& iso_frame, const std::vector<uint8_t>& data);
};

} // namespace iso15765