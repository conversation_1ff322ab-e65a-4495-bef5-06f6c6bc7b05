#include "can_frame_parser.h"
#include <fstream>
#include <sstream>
#include <iomanip>
#include <algorithm>
#include <cctype>

namespace iso15765 {

// CANFrame implementation
CANFrame::CANFrame(uint16_t can_id, const std::vector<uint8_t>& data_bytes)
    : can_id_(can_id), data_bytes_(data_bytes) {
}

std::string CANFrame::toString() const {
    std::ostringstream oss;
    oss << "CAN ID: 0x" << std::hex << std::uppercase << std::setfill('0') << std::setw(3) << can_id_
        << ", DLC: " << std::dec << data_bytes_.size() << ", Data: [";

    for (size_t i = 0; i < data_bytes_.size(); ++i) {
        if (i > 0) oss << " ";
        oss << std::hex << std::uppercase << std::setfill('0') << std::setw(2) << static_cast<int>(data_bytes_[i]);
    }
    oss << "]";
    return oss.str();
}

// CANFrameParser implementation
CANFrame CANFrameParser::parseLine(const std::string& line) {
    // Remove whitespace and convert to uppercase
    std::string trimmed_line = line;
    trimmed_line.erase(std::remove_if(trimmed_line.begin(), trimmed_line.end(), ::isspace), trimmed_line.end());
    std::transform(trimmed_line.begin(), trimmed_line.end(), trimmed_line.begin(), ::toupper);

    if (trimmed_line.length() != 19) {
        throw std::invalid_argument("Invalid line length: " + std::to_string(trimmed_line.length()) + ", expected 19 characters");
    }

    try {
        // Extract CAN ID (first 3 hex characters)
        std::string can_id_str = trimmed_line.substr(0, 3);
        uint16_t can_id = static_cast<uint16_t>(hexToInt(can_id_str));

        // Extract data bytes (remaining 16 hex characters = 8 bytes)
        std::string data_str = trimmed_line.substr(3);
        if (data_str.length() != 16) {
            throw std::invalid_argument("Invalid data length: " + std::to_string(data_str.length()) + ", expected 16 characters");
        }

        // Convert hex string to bytes
        std::vector<uint8_t> data_bytes;
        data_bytes.reserve(8);

        for (size_t i = 0; i < 16; i += 2) {
            std::string byte_str = data_str.substr(i, 2);
            uint8_t byte_val = static_cast<uint8_t>(hexToInt(byte_str));
            data_bytes.push_back(byte_val);
        }

        return CANFrame(can_id, data_bytes);

    } catch (const std::exception& e) {
        throw std::invalid_argument("Failed to parse line '" + line + "': " + e.what());
    }
}

std::vector<CANFrame> CANFrameParser::parseFile(const std::string& filename) {
    std::ifstream file(filename);
    if (!file.is_open()) {
        throw std::runtime_error("Cannot open file: " + filename);
    }

    std::vector<CANFrame> frames;
    std::string line;
    size_t line_num = 0;

    while (std::getline(file, line)) {
        ++line_num;

        // Skip empty lines
        if (line.empty() || std::all_of(line.begin(), line.end(), ::isspace)) {
            continue;
        }

        try {
            CANFrame frame = parseLine(line);
            frames.push_back(frame);
        } catch (const std::exception& e) {
            // Log warning but continue processing
            // In a real application, you might want to use a proper logging framework
            // For now, we'll just ignore the error and continue
        }
    }

    return frames;
}

uint32_t CANFrameParser::hexToInt(const std::string& hex_str) {
    if (hex_str.empty()) {
        throw std::invalid_argument("Empty hex string");
    }

    uint32_t result = 0;
    for (char c : hex_str) {
        result = (result << 4) + hexCharToInt(c);
    }
    return result;
}

uint8_t CANFrameParser::hexCharToInt(char hex_char) {
    if (hex_char >= '0' && hex_char <= '9') {
        return static_cast<uint8_t>(hex_char - '0');
    } else if (hex_char >= 'A' && hex_char <= 'F') {
        return static_cast<uint8_t>(hex_char - 'A' + 10);
    } else if (hex_char >= 'a' && hex_char <= 'f') {
        return static_cast<uint8_t>(hex_char - 'a' + 10);
    } else {
        throw std::invalid_argument("Invalid hex character: " + std::string(1, hex_char));
    }
}

} // namespace iso15765