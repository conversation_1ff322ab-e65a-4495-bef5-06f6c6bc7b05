/**
 * @file main.cpp
 * @brief ISO 15765-2 Transcript Parser - Main Application
 *
 * A C++ implementation of the ISO 15765-2 (ISO-TP) transport protocol
 * for decoding diagnostic messages from CAN bus transcripts.
 *
 * Usage:
 *   iso15765_parser [options] [filename]
 *
 * Options:
 *   -v, --verbose    Enable verbose output showing all frames
 *   -s, --stats      Show parsing statistics
 *   -h, --help       Show help message
 *
 * If no filename is specified, it will look for 'transcript.txt' in the current directory.
 */

#include "transcript_parser.h"
#include <iostream>
#include <string>
#include <vector>
#include <algorithm>
#include <iomanip>

void printUsage(const std::string& program_name) {
    std::cout << "ISO 15765-2 Transcript Parser\n";
    std::cout << "=============================\n\n";
    std::cout << "Usage: " << program_name << " [options] [filename]\n\n";
    std::cout << "Parse ISO 15765-2 transcript files and decode diagnostic messages\n\n";
    std::cout << "Options:\n";
    std::cout << "  -v, --verbose    Enable verbose output showing all frames\n";
    std::cout << "  -s, --stats      Show parsing statistics\n";
    std::cout << "  -h, --help       Show this help message\n\n";
    std::cout << "Arguments:\n";
    std::cout << "  filename         Transcript file to parse (default: transcript.txt)\n\n";
    std::cout << "Examples:\n";
    std::cout << "  " << program_name << "                    # Parse transcript.txt\n";
    std::cout << "  " << program_name << " data.txt           # Parse data.txt\n";
    std::cout << "  " << program_name << " -v transcript.txt  # Verbose output\n";
    std::cout << "  " << program_name << " -s -v data.txt     # Verbose + statistics\n\n";
    std::cout << "Transcript File Format:\n";
    std::cout << "  Each line: XXXDDDDDDDDDDDDDDDD (19 hex characters)\n";
    std::cout << "  XXX = 3-character CAN ID\n";
    std::cout << "  DDDDDDDDDDDDDDDD = 16-character data payload (8 bytes)\n\n";
}

int main(int argc, char* argv[]) {
    // Parse command line arguments
    bool verbose = false;
    bool show_stats = false;
    std::string filename = "transcript.txt";

    std::vector<std::string> args(argv + 1, argv + argc);

    for (size_t i = 0; i < args.size(); ++i) {
        const std::string& arg = args[i];

        if (arg == "-h" || arg == "--help") {
            printUsage(argv[0]);
            return 0;
        } else if (arg == "-v" || arg == "--verbose") {
            verbose = true;
        } else if (arg == "-s" || arg == "--stats") {
            show_stats = true;
        } else if (arg.front() == '-') {
            std::cerr << "Error: Unknown option '" << arg << "'" << std::endl;
            std::cerr << "Use -h or --help for usage information." << std::endl;
            return 1;
        } else {
            // Assume it's a filename
            filename = arg;
        }
    }

    try {
        // Create parser and process file
        iso15765::TranscriptParser parser(verbose);
        auto messages = parser.parseFile(filename);

        // Show statistics if requested
        if (show_stats || verbose) {
            std::cout << std::endl;
            std::cout << parser.getStatistics().toString() << std::endl;
        }

        // Summary
        std::cout << std::endl;
        std::cout << "Successfully decoded " << messages.size()
                  << " ISO-TP messages from " << filename << std::endl;

        return messages.empty() ? 1 : 0;

    } catch (const std::exception& e) {
        std::cerr << "Error: " << e.what() << std::endl;
        return 1;
    }
}

// Alternative main function for simple file output (used by build.bat)
#ifdef SIMPLE_OUTPUT
int main(int argc, char* argv[]) {
    std::string filename = (argc > 1) ? argv[1] : "transcript.txt";

    try {
        iso15765::TranscriptParser parser(false);  // Non-verbose mode
        auto messages = parser.parseFile(filename);

        // Simple output format for comparison with Python version
        for (size_t i = 0; i < messages.size(); ++i) {
            std::cout << "Message " << std::setw(2) << (i + 1) << ": "
                      << messages[i]->toString() << std::endl;
        }

        std::cout << std::endl;
        std::cout << "Successfully decoded " << messages.size()
                  << " ISO-TP messages from " << filename << std::endl;

        return 0;

    } catch (const std::exception& e) {
        std::cerr << "Error: " << e.what() << std::endl;
        return 1;
    }
}
#endif