# ISO 15765-2 Transcript Parser

A Python implementation of the ISO 15765-2 (ISO-TP) transport protocol for decoding diagnostic messages from CAN bus transcripts.

## Overview

This parser implements the ISO 15765-2 standard for diagnostic communication over Controller Area Network (CAN). It can decode CAN frames containing ISO-TP messages and reassemble multi-frame diagnostic messages according to the standard.

### Features

- ✅ **Complete ISO 15765-2 Implementation**
  - Single Frame (SF) decoding
  - Multi-frame message reassembly (First Frame + Consecutive Frames)
  - Flow Control (FC) frame handling
  - Sequence number validation
  - Message length validation

- ✅ **Robust Error Handling**
  - Malformed frame detection
  - Sequence error detection
  - Protocol violation reporting
  - Graceful error recovery

- ✅ **Multiple Output Modes**
  - Standard output (decoded messages only)
  - Verbose output (all frames + messages)
  - Statistics reporting
  - Command-line interface

## File Structure

```
├── iso15765_parser.py      # Main parser application
├── can_frame_parser.py     # CAN frame parsing module
├── iso_tp_decoder.py       # ISO-TP protocol decoder
├── transcript.txt          # Sample transcript file
└── README.md              # This documentation
```

## Installation

No external dependencies required. Uses only Python standard library.

**Requirements:**
- Python 3.7 or higher
- Standard library modules: `enum`, `dataclasses`, `typing`, `sys`, `argparse`

## Usage

### Basic Usage

```bash
# Parse transcript.txt (default)
python iso15765_parser.py

# Parse a specific file
python iso15765_parser.py my_transcript.txt

# Verbose output showing all frames
python iso15765_parser.py -v transcript.txt

# Show parsing statistics
python iso15765_parser.py -s transcript.txt

# Combine verbose and statistics
python iso15765_parser.py -v -s transcript.txt
```

### Command Line Options

```
usage: iso15765_parser.py [-h] [-v] [-s] [filename]

Parse ISO 15765-2 transcript files and decode diagnostic messages

positional arguments:
  filename       Transcript file to parse (default: transcript.txt)

options:
  -h, --help     show this help message and exit
  -v, --verbose  Enable verbose output showing all frames
  -s, --stats    Show parsing statistics
```

## Transcript File Format

The parser expects transcript files with the following format:

```
7400210C00000000000
7600650C0003201F4AA
7400221830000000000
760101A618339484D31
...
```

**Format Specification:**
- Each line represents one CAN frame
- 19 characters per line (hexadecimal)
- Format: `XXXDDDDDDDDDDDDDDDD`
  - `XXX`: 3-character CAN ID (11-bit)
  - `DDDDDDDDDDDDDDDD`: 16-character data payload (8 bytes)

## ISO 15765-2 Protocol Implementation

### Frame Types

The parser implements all four ISO-TP frame types:

#### 1. Single Frame (SF)
- **PCI**: `0x0X` (where X = data length 1-7)
- **Usage**: Complete message fits in one CAN frame
- **Example**: `02 10 C0` → SF with 2 bytes: `10 C0`

#### 2. First Frame (FF)
- **PCI**: `0x1X XX` (where XXX = total message length)
- **Usage**: Start of multi-frame message
- **Example**: `10 1A 61 83 39 48 4D 31` → FF, total length 26 bytes

#### 3. Consecutive Frame (CF)
- **PCI**: `0x2X` (where X = sequence number 1-15)
- **Usage**: Continuation of multi-frame message
- **Example**: `21 41 34 11 01 00 02 65` → CF sequence 1

#### 4. Flow Control (FC)
- **PCI**: `0x3X` (where X = flow status)
- **Usage**: Control transmission of consecutive frames
- **Example**: `30 00 00` → Continue to send, no delay

### Message Reassembly

The parser correctly handles:

1. **Sequence Validation**: Ensures CF frames arrive in correct order (1,2,3...F,0,1...)
2. **Length Validation**: Verifies total message length matches FF declaration
3. **Data Trimming**: Removes padding bytes to exact message length
4. **Multiple Sessions**: Handles concurrent multi-frame messages on different CAN IDs

## Example Output

### Standard Output
```
Parsing transcript file: transcript.txt
============================================================
Found 33 CAN frames

Message  1: CAN ID 0x740 (1 frames, 2 bytes): 10 C0
Message  2: CAN ID 0x760 (1 frames, 6 bytes): 50 C0 00 32 01 F4
Message  3: CAN ID 0x740 (1 frames, 2 bytes): 21 83
Message  4: CAN ID 0x760 (4 frames, 26 bytes): 61 83 39 48 4D 31 41 34 11 01 00 02 65 95 61 65 29 20 12 03 00 00 00 00 00 80
...

Successfully decoded 21 ISO-TP messages from transcript.txt
```

### Verbose Output
```
Frame  1: CAN 0x740 [02 10 C0 00 00 00 00 00] -> SF(len=2): 10 C0
Message  1: CAN ID 0x740 (1 frames, 2 bytes): 10 C0

Frame  4: CAN 0x760 [10 1A 61 83 39 48 4D 31] -> FF(total=26): 61 83 39 48 4D 31...
Frame  5: CAN 0x740 [30 00 00 00 00 00 00 00] -> FC(status=CONTINUE_TO_SEND, BS=0, STmin=0)
Frame  6: CAN 0x760 [21 41 34 11 01 00 02 65] -> CF(seq=1): 41 34 11 01 00 02 65
...
```

### Statistics Output
```
Parsing Statistics:
------------------------------
Total CAN frames:      33
Single frames:         18
First frames:          3
Consecutive frames:    9
Flow control frames:   3
Complete messages:     21
Parse errors:          0
Message efficiency:    63.6%
```

## Diagnostic Message Analysis

The parser successfully decodes various UDS (Unified Diagnostic Services) messages:

### Common Message Types Found

1. **Diagnostic Session Control (0x10)**
   - Request: `10 C0` (Enter default session)
   - Response: `50 C0 00 32 01 F4` (Positive response with timing parameters)

2. **Read Data By Identifier (0x21/0x22)**
   - Request: `21 83` (Read PID 0x1883)
   - Request: `22 F1 A0` (Read PID 0xF1A0)
   - Response: `61 83 39 48 4D 31...` (Multi-frame VIN data)

3. **Negative Response (0x7F)**
   - Response: `7F 21 12` (Service 0x21 not supported)
   - Response: `7F 22 12` (Service 0x22 not supported)

### CAN ID Mapping

The transcript contains communication with multiple ECUs:

- **0x740/0x760**: Primary ECU (likely Engine Control Module)
- **0x743/0x763**: Secondary ECU (likely Transmission Control Module)
- **0x7E0/0x7E8**: Another ECU (likely Body Control Module)
- **0x710**: Broadcast or gateway messages

## Technical Implementation Details

### Architecture

The parser is designed with a modular architecture:

```
┌─────────────────────┐
│  iso15765_parser.py │  ← Main application & CLI
└─────────┬───────────┘
          │
┌─────────▼───────────┐
│  can_frame_parser.py│  ← CAN frame parsing
└─────────┬───────────┘
          │
┌─────────▼───────────┐
│  iso_tp_decoder.py  │  ← ISO-TP protocol logic
└─────────────────────┘
```

### Key Classes

#### `CANFrame`
- Represents a single CAN frame
- Properties: `can_id`, `data_bytes`, `dlc`

#### `ISOTPFrame`
- Represents a decoded ISO-TP frame
- Frame type specific fields (SF, FF, CF, FC)
- Validation and error checking

#### `ISOTPMessage`
- Represents a complete reassembled message
- Properties: `can_id`, `data`, `frame_count`

#### `ISOTPDecoder`
- Main protocol decoder
- Handles message reassembly
- Manages pending multi-frame messages

### Error Handling

The parser includes comprehensive error handling:

- **Format Validation**: Checks line length, hex format
- **Protocol Validation**: Validates PCI fields, sequence numbers
- **Length Validation**: Ensures data lengths match declarations
- **Sequence Validation**: Detects missing or out-of-order frames
- **Graceful Recovery**: Continues processing after errors

## Testing

The parser has been tested with the provided transcript and successfully:

- ✅ Parsed 33 CAN frames without errors
- ✅ Decoded 21 complete ISO-TP messages
- ✅ Correctly reassembled 3 multi-frame messages
- ✅ Handled 4 different CAN ID pairs
- ✅ Validated all sequence numbers and message lengths

## Limitations

- **Normal Addressing Only**: Currently supports only normal addressing (not extended addressing)
- **CAN 2.0A**: Supports 11-bit CAN IDs only (not 29-bit extended IDs)
- **No Timing**: Does not enforce ISO-TP timing requirements (N_As, N_Ar, etc.)
- **No Flow Control Logic**: Recognizes FC frames but doesn't implement transmission control

## Future Enhancements

Potential improvements for production use:

1. **Extended Addressing Support**: Add support for extended addressing mode
2. **CAN FD Support**: Support for CAN FD frames with larger payloads
3. **Timing Validation**: Implement ISO-TP timing parameter validation
4. **Real-time Processing**: Support for live CAN bus monitoring
5. **Export Formats**: Add JSON, CSV, or XML output options
6. **GUI Interface**: Graphical user interface for easier use

## References

- [ISO 15765-2:2016](https://www.iso.org/standard/66574.html) - Road vehicles — Diagnostic communication over Controller Area Network (DoCAN) — Part 2: Transport protocol and network layer services
- [Wikipedia: ISO 15765-2](https://en.wikipedia.org/wiki/ISO_15765-2) - Overview of the ISO-TP protocol
- [UDS Protocol](https://en.wikipedia.org/wiki/Unified_Diagnostic_Services) - Unified Diagnostic Services standard

## License

This implementation is provided for educational and research purposes. Please ensure compliance with relevant standards and regulations when using in production environments.

---

**Author**: Augment Agent
**Date**: September 2025
**Version**: 1.0