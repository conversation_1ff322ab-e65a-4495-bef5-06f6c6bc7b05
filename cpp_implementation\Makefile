# ============================================================================
# ISO 15765-2 Transcript Parser - Makefile
# ============================================================================
#
# Alternative build system for environments without CMake
# Supports GCC, Clang, and MSVC (via cl.exe)
#
# Usage:
#   make                 # Build default target
#   make all             # Build all targets
#   make clean           # Clean build files
#   make run             # Build and run with transcript.txt
#   make test            # Build and run tests
#
# Variables:
#   CXX=g++              # Specify compiler
#   DEBUG=1              # Enable debug build
#   VERBOSE=1            # Enable verbose output
# ============================================================================

# Compiler detection and configuration
CXX ?= g++
TARGET = iso15765_parser
TARGET_SIMPLE = iso15765_parser_simple

# Source and header files
SOURCES = can_frame_parser.cpp iso_tp_decoder.cpp transcript_parser.cpp main.cpp
HEADERS = can_frame_parser.h iso_tp_decoder.h transcript_parser.h
OBJECTS = $(SOURCES:.cpp=.o)
OBJECTS_SIMPLE = $(SOURCES:.cpp=_simple.o)

# Build directories
BUILD_DIR = build
BIN_DIR = $(BUILD_DIR)/bin
OBJ_DIR = $(BUILD_DIR)/obj

# Compiler flags
CXXFLAGS = -std=c++17 -Wall -Wextra -Wpedantic
LDFLAGS =

# Debug/Release configuration
ifdef DEBUG
    CXXFLAGS += -g -O0 -DDEBUG
    BUILD_TYPE = Debug
else
    CXXFLAGS += -O2 -DNDEBUG
    BUILD_TYPE = Release
endif

# Verbose output
ifdef VERBOSE
    CXXFLAGS += -v
    Q =
else
    Q = @
endif

# Platform-specific settings
ifeq ($(OS),Windows_NT)
    TARGET_EXT = .exe
    RM = del /Q
    RMDIR = rmdir /S /Q
    MKDIR = mkdir
    COPY = copy
    PATHSEP = \\
else
    TARGET_EXT =
    RM = rm -f
    RMDIR = rm -rf
    MKDIR = mkdir -p
    COPY = cp
    PATHSEP = /
endif

# Full target names
TARGET_FULL = $(BIN_DIR)/$(TARGET)$(TARGET_EXT)
TARGET_SIMPLE_FULL = $(BIN_DIR)/$(TARGET_SIMPLE)$(TARGET_EXT)

# Default target
.PHONY: all clean run test help
.DEFAULT_GOAL := all

all: $(TARGET_FULL) $(TARGET_SIMPLE_FULL)

# Create directories
$(BUILD_DIR):
	$(Q)$(MKDIR) $(BUILD_DIR)

$(BIN_DIR): $(BUILD_DIR)
	$(Q)$(MKDIR) $(BIN_DIR)

$(OBJ_DIR): $(BUILD_DIR)
	$(Q)$(MKDIR) $(OBJ_DIR)

# Main executable
$(TARGET_FULL): $(addprefix $(OBJ_DIR)/,$(OBJECTS)) | $(BIN_DIR)
	@echo "Linking $(TARGET)..."
	$(Q)$(CXX) $(LDFLAGS) -o $@ $^

# Simple output executable
$(TARGET_SIMPLE_FULL): $(addprefix $(OBJ_DIR)/,$(OBJECTS_SIMPLE)) | $(BIN_DIR)
	@echo "Linking $(TARGET_SIMPLE)..."
	$(Q)$(CXX) $(LDFLAGS) -o $@ $^

# Object files for main executable
$(OBJ_DIR)/%.o: %.cpp $(HEADERS) | $(OBJ_DIR)
	@echo "Compiling $< ($(BUILD_TYPE))..."
	$(Q)$(CXX) $(CXXFLAGS) -c -o $@ $<

# Object files for simple executable
$(OBJ_DIR)/%_simple.o: %.cpp $(HEADERS) | $(OBJ_DIR)
	@echo "Compiling $< ($(BUILD_TYPE), Simple)..."
	$(Q)$(CXX) $(CXXFLAGS) -DSIMPLE_OUTPUT -c -o $@ $<

# Clean build files
clean:
	@echo "Cleaning build files..."
	$(Q)$(RMDIR) $(BUILD_DIR) 2>nul || true

# Build and run with transcript.txt
run: $(TARGET_FULL)
	@echo "Running $(TARGET) with transcript.txt..."
	$(Q)$(COPY) ..$(PATHSEP)transcript.txt $(BIN_DIR)$(PATHSEP)transcript.txt
	$(Q)cd $(BIN_DIR) && .$(PATHSEP)$(TARGET)$(TARGET_EXT) transcript.txt

# Build and run tests
test: $(TARGET_SIMPLE_FULL)
	@echo "Running tests..."
	$(Q)$(COPY) ..$(PATHSEP)transcript.txt $(BIN_DIR)$(PATHSEP)transcript.txt
	$(Q)cd $(BIN_DIR) && .$(PATHSEP)$(TARGET_SIMPLE)$(TARGET_EXT) transcript.txt > ..$(PATHSEP)..$(PATHSEP)..$(PATHSEP)parsed.txt
	@echo "Test output saved to parsed.txt"

# Show help
help:
	@echo "ISO 15765-2 Transcript Parser - Makefile"
	@echo "========================================"
	@echo ""
	@echo "Targets:"
	@echo "  all      - Build all executables (default)"
	@echo "  clean    - Clean build files"
	@echo "  run      - Build and run with transcript.txt"
	@echo "  test     - Build and run tests, output to parsed.txt"
	@echo "  help     - Show this help"
	@echo ""
	@echo "Variables:"
	@echo "  CXX=g++     - Specify compiler (g++, clang++, cl)"
	@echo "  DEBUG=1     - Enable debug build"
	@echo "  VERBOSE=1   - Enable verbose output"
	@echo ""
	@echo "Examples:"
	@echo "  make                    # Build with default settings"
	@echo "  make CXX=clang++        # Build with Clang"
	@echo "  make DEBUG=1            # Debug build"
	@echo "  make clean all          # Clean and rebuild"
	@echo "  make run                # Build and run"

# Print build information
info:
	@echo "Build Configuration:"
	@echo "  Compiler: $(CXX)"
	@echo "  Build Type: $(BUILD_TYPE)"
	@echo "  C++ Standard: C++17"
	@echo "  Flags: $(CXXFLAGS)"
	@echo "  Target: $(TARGET_FULL)"