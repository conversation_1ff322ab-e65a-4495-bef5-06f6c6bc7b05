cmake_minimum_required(VERSION 3.10)

# Project configuration
project(ISO15765Parser
    VERSION 1.0.0
    DESCRIPTION "ISO 15765-2 Transcript Parser"
    LANGUAGES CXX)

# Set C++ standard
set(CMAKE_CXX_STANDARD 17)
set(CMAKE_CXX_STANDARD_REQUIRED ON)
set(CMAKE_CXX_EXTENSIONS OFF)

# Compiler-specific options
if(MSVC)
    # Visual Studio specific options
    add_compile_options(/W4)
    add_compile_definitions(_CRT_SECURE_NO_WARNINGS)
else()
    # GCC/Clang options
    add_compile_options(-Wall -Wextra -Wpedantic)
endif()

# Source files
set(SOURCES
    can_frame_parser.cpp
    iso_tp_decoder.cpp
    transcript_parser.cpp
    main.cpp
)

# Header files (for IDE support)
set(HEADERS
    can_frame_parser.h
    iso_tp_decoder.h
    transcript_parser.h
)

# Create the main executable
add_executable(iso15765_parser ${SOURCES} ${HEADERS})

# Set output directory
set_target_properties(iso15765_parser PROPERTIES
    RUNTIME_OUTPUT_DIRECTORY ${CMAKE_BINARY_DIR}/bin
)

# Create a simple output version for automated testing
add_executable(iso15765_parser_simple ${SOURCES} ${HEADERS})
target_compile_definitions(iso15765_parser_simple PRIVATE SIMPLE_OUTPUT)
set_target_properties(iso15765_parser_simple PROPERTIES
    RUNTIME_OUTPUT_DIRECTORY ${CMAKE_BINARY_DIR}/bin
)

# Installation rules
install(TARGETS iso15765_parser iso15765_parser_simple
    RUNTIME DESTINATION bin
)

# Copy transcript file to build directory for testing
configure_file(${CMAKE_SOURCE_DIR}/../transcript.txt
               ${CMAKE_BINARY_DIR}/bin/transcript.txt
               COPYONLY)

# Print build information
message(STATUS "ISO 15765-2 Parser Configuration:")
message(STATUS "  C++ Standard: ${CMAKE_CXX_STANDARD}")
message(STATUS "  Compiler: ${CMAKE_CXX_COMPILER_ID}")
message(STATUS "  Build Type: ${CMAKE_BUILD_TYPE}")
message(STATUS "  Output Directory: ${CMAKE_BINARY_DIR}/bin")