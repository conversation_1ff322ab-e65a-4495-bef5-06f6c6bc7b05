#pragma once

#include "can_frame_parser.h"
#include "iso_tp_decoder.h"
#include <vector>
#include <string>
#include <memory>

namespace iso15765 {

/**
 * @brief Statistics for parsing operations
 */
struct ParsingStatistics {
    size_t total_frames = 0;
    size_t single_frames = 0;
    size_t first_frames = 0;
    size_t consecutive_frames = 0;
    size_t flow_control_frames = 0;
    size_t complete_messages = 0;
    size_t parse_errors = 0;

    /**
     * @brief Convert statistics to string representation
     * @return String representation of statistics
     */
    std::string toString() const;
};

/**
 * @brief Main parser for ISO 15765-2 transcript files
 */
class TranscriptParser {
public:
    /**
     * @brief Construct a transcript parser
     * @param verbose Enable verbose output
     */
    explicit TranscriptParser(bool verbose = false);

    /**
     * @brief Parse a transcript file and return all complete ISO-TP messages
     * @param filename Path to transcript file
     * @return Vector of complete ISO-TP messages
     */
    std::vector<std::unique_ptr<ISOTPMessage>> parseFile(const std::string& filename);

    /**
     * @brief Get parsing statistics
     * @return Reference to statistics
     */
    const ParsingStatistics& getStatistics() const { return stats_; }

    /**
     * @brief Reset statistics
     */
    void resetStatistics();

    /**
     * @brief Set verbose mode
     * @param verbose Enable/disable verbose output
     */
    void setVerbose(bool verbose) { verbose_ = verbose; }

private:
    CANFrameParser can_parser_;
    ISOTPDecoder iso_decoder_;
    bool verbose_;
    ParsingStatistics stats_;

    /**
     * @brief Update statistics based on frame type
     * @param iso_frame ISO-TP frame to analyze
     */
    void updateStats(const ISOTPFrame& iso_frame);

    /**
     * @brief Format frame for verbose output
     * @param can_frame Original CAN frame
     * @param iso_frame Decoded ISO-TP frame
     * @return Formatted string
     */
    std::string formatFrameVerbose(const CANFrame& can_frame, const ISOTPFrame& iso_frame) const;

    /**
     * @brief Format complete message for output
     * @param message Complete ISO-TP message
     * @return Formatted string
     */
    std::string formatMessage(const ISOTPMessage& message) const;
};

} // namespace iso15765