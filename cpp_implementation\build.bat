@echo off
REM ============================================================================
REM ISO 15765-2 Transcript Parser - Build and Run Script
REM ============================================================================
REM
REM This script builds the C++ ISO 15765-2 parser and runs it with the
REM transcript.txt file, outputting results to parsed.txt for comparison
REM with the Python implementation.
REM
REM Requirements:
REM   - CMake 3.10 or later
REM   - C++17 compatible compiler (MSVC, GCC, or Clang)
REM   - transcript.txt file in parent directory
REM
REM Usage:
REM   build.bat [clean|rebuild|run|help]
REM
REM Commands:
REM   clean   - Clean build directory
REM   rebuild - Clean and build
REM   run     - Build and run parser
REM   help    - Show this help
REM   (none)  - Default: build and run
REM ============================================================================

setlocal enabledelayedexpansion

REM Configuration
set BUILD_DIR=build
set BIN_DIR=%BUILD_DIR%\bin
set OUTPUT_FILE=parsed.txt
set TRANSCRIPT_FILE=..\transcript.txt

REM Parse command line arguments
set COMMAND=%1
if "%COMMAND%"=="" set COMMAND=run
if "%COMMAND%"=="help" goto :show_help
if "%COMMAND%"=="clean" goto :clean
if "%COMMAND%"=="rebuild" goto :rebuild
if "%COMMAND%"=="run" goto :build_and_run

echo Error: Unknown command '%COMMAND%'
echo Use 'build.bat help' for usage information.
exit /b 1

:show_help
echo.
echo ISO 15765-2 Transcript Parser - Build Script
echo ============================================
echo.
echo Usage: build.bat [command]
echo.
echo Commands:
echo   clean   - Clean build directory
echo   rebuild - Clean and build
echo   run     - Build and run parser (default)
echo   help    - Show this help
echo.
echo The script will:
echo   1. Build the C++ parser using CMake
echo   2. Run it with transcript.txt
echo   3. Output results to parsed.txt
echo.
goto :eof

:clean
echo Cleaning build directory...
if exist "%BUILD_DIR%" (
    rmdir /s /q "%BUILD_DIR%"
    echo Build directory cleaned.
) else (
    echo Build directory does not exist.
)
goto :eof

:rebuild
call :clean
goto :build_and_run

:build_and_run
echo.
echo ============================================================================
echo Building ISO 15765-2 Transcript Parser
echo ============================================================================

REM Check if transcript file exists
if not exist "%TRANSCRIPT_FILE%" (
    echo Error: transcript.txt not found in parent directory
    echo Please ensure transcript.txt exists at: %TRANSCRIPT_FILE%
    exit /b 1
)

REM Create build directory
if not exist "%BUILD_DIR%" mkdir "%BUILD_DIR%"

REM Configure with CMake
echo.
echo Configuring with CMake...
cd "%BUILD_DIR%"
cmake .. -DCMAKE_BUILD_TYPE=Release
if errorlevel 1 (
    echo Error: CMake configuration failed
    cd ..
    exit /b 1
)

REM Build the project
echo.
echo Building project...
cmake --build . --config Release
if errorlevel 1 (
    echo Error: Build failed
    cd ..
    exit /b 1
)

cd ..

REM Check if executable was created
if not exist "%BIN_DIR%\iso15765_parser.exe" (
    echo Error: Executable not found at %BIN_DIR%\iso15765_parser.exe
    exit /b 1
)

echo.
echo ============================================================================
echo Running ISO 15765-2 Parser
echo ============================================================================

REM Copy transcript file to build directory
copy "%TRANSCRIPT_FILE%" "%BIN_DIR%\transcript.txt" >nul

REM Run the parser and redirect output to parsed.txt
cd "%BIN_DIR%"
echo Running parser with transcript.txt...
iso15765_parser.exe transcript.txt > ..\..\..\%OUTPUT_FILE% 2>&1
set PARSER_EXIT_CODE=!errorlevel!

cd ..\..\..

REM Check results
if !PARSER_EXIT_CODE! equ 0 (
    echo.
    echo ============================================================================
    echo SUCCESS: Parser completed successfully
    echo ============================================================================
    echo.
    echo Output saved to: %OUTPUT_FILE%
    echo.
    echo First few lines of output:
    echo ------------------------
    type "%OUTPUT_FILE%" | more +1 | head -10 2>nul || (
        REM Fallback for systems without head command
        powershell "Get-Content '%OUTPUT_FILE%' | Select-Object -First 10"
    )
    echo.
    echo Full output available in: %OUTPUT_FILE%
) else (
    echo.
    echo ============================================================================
    echo ERROR: Parser failed with exit code !PARSER_EXIT_CODE!
    echo ============================================================================
    echo.
    echo Error output:
    echo -------------
    type "%OUTPUT_FILE%"
    exit /b !PARSER_EXIT_CODE!
)

echo.
echo Build and run completed successfully.
goto :eof