Parsing transcript file: transcript.txt
============================================================
Found 33 CAN frames

Message  1: CAN ID 0x740 (1 frames, 2 bytes): 10 C0
Message  2: CAN ID 0x760 (1 frames, 6 bytes): 50 C0 00 32 01 F4
Message  3: CAN ID 0x740 (1 frames, 2 bytes): 21 83
Message  4: CAN ID 0x760 (4 frames, 26 bytes): 61 83 39 48 4D 31 41 34 11 01 00 02 65 95 61 65 29 20 12 03 00 00 00 00 00 80
Message  5: CAN ID 0x743 (1 frames, 2 bytes): 10 C0
Message  6: CAN ID 0x763 (1 frames, 6 bytes): 50 C0 00 32 01 F4
Message  7: CAN ID 0x743 (1 frames, 2 bytes): 21 83
Message  8: CAN ID 0x763 (4 frames, 26 bytes): 61 83 39 48 50 30 41 16 42 51 7F 00 92 14 19 01 00 06 20 03 00 00 00 00 00 80
Message  9: CAN ID 0x743 (1 frames, 2 bytes): 21 80
Message 10: CAN ID 0x763 (1 frames, 3 bytes): 7F 21 12
Message 11: CAN ID 0x7E0 (1 frames, 2 bytes): 10 C0
Message 12: CAN ID 0x7E8 (1 frames, 6 bytes): 50 C0 00 32 01 F4
Message 13: CAN ID 0x7E0 (1 frames, 2 bytes): 21 81
Message 14: CAN ID 0x7E8 (4 frames, 21 bytes): 61 81 31 4E 34 41 4C 33 41 50 39 46 4E 39 30 30 31 36 33 00 00
Message 15: CAN ID 0x710 (1 frames, 2 bytes): 10 C0
Message 16: CAN ID 0x710 (1 frames, 2 bytes): 10 C0
Message 17: CAN ID 0x710 (1 frames, 2 bytes): 10 C0
Message 18: CAN ID 0x7E0 (1 frames, 2 bytes): 10 C0
Message 19: CAN ID 0x7E8 (1 frames, 6 bytes): 50 C0 00 32 01 F4
Message 20: CAN ID 0x7E0 (1 frames, 3 bytes): 22 F1 A0
Message 21: CAN ID 0x7E8 (1 frames, 3 bytes): 7F 22 12

Successfully decoded 21 ISO-TP messages from transcript.txt
