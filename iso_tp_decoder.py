#!/usr/bin/env python3
"""
ISO 15765-2 (ISO-TP) Protocol Decoder

This module implements the ISO-TP transport protocol for decoding
diagnostic messages over CAN bus according to ISO 15765-2 standard.

Frame Types:
- Single Frame (SF): PCI = 0x0X, complete message in one frame
- First Frame (FF): PCI = 0x1X, start of multi-frame message
- Consecutive Frame (CF): PCI = 0x2X, continuation of multi-frame message
- Flow Control (FC): PCI = 0x3X, flow control for multi-frame messages
"""

from enum import Enum
from dataclasses import dataclass
from typing import List, Optional, Dict, Tuple
from can_frame_parser import CANFrame


class FrameType(Enum):
    """ISO-TP frame types"""
    SINGLE_FRAME = 0x0
    FIRST_FRAME = 0x1
    CONSECUTIVE_FRAME = 0x2
    FLOW_CONTROL = 0x3


class FlowStatus(Enum):
    """Flow control status values"""
    CONTINUE_TO_SEND = 0x0
    WAIT = 0x1
    OVERFLOW_ABORT = 0x2


@dataclass
class ISOTPFrame:
    """Represents a decoded ISO-TP frame"""
    frame_type: FrameType
    can_id: int
    raw_data: List[int]

    # Single Frame fields
    sf_length: Optional[int] = None
    sf_data: Optional[List[int]] = None

    # First Frame fields
    ff_length: Optional[int] = None
    ff_data: Optional[List[int]] = None

    # Consecutive Frame fields
    cf_sequence: Optional[int] = None
    cf_data: Optional[List[int]] = None

    # Flow Control fields
    fc_status: Optional[FlowStatus] = None
    fc_block_size: Optional[int] = None
    fc_separation_time: Optional[int] = None


@dataclass
class ISOTPMessage:
    """Represents a complete ISO-TP message (potentially reassembled from multiple frames)"""
    can_id: int
    data: List[int]
    frame_count: int

    def __str__(self):
        data_hex = ' '.join(f'{b:02X}' for b in self.data)
        return f"ISO-TP Message [CAN ID: 0x{self.can_id:03X}, Length: {len(self.data)}, Frames: {self.frame_count}]: {data_hex}"


class ISOTPDecoder:
    """ISO-TP protocol decoder"""

    def __init__(self):
        self.pending_messages: Dict[int, Dict] = {}  # CAN ID -> message state

    def decode_frame(self, can_frame: CANFrame) -> ISOTPFrame:
        """
        Decode a single CAN frame into an ISO-TP frame

        Args:
            can_frame: CAN frame to decode

        Returns:
            ISOTPFrame: Decoded ISO-TP frame

        Raises:
            ValueError: If frame format is invalid
        """
        if len(can_frame.data_bytes) == 0:
            raise ValueError("Empty CAN frame data")

        # Extract PCI (Protocol Control Information) from first byte
        pci_byte = can_frame.data_bytes[0]
        frame_type_val = (pci_byte >> 4) & 0x0F

        try:
            frame_type = FrameType(frame_type_val)
        except ValueError:
            raise ValueError(f"Invalid frame type: 0x{frame_type_val:X}")

        iso_frame = ISOTPFrame(
            frame_type=frame_type,
            can_id=can_frame.can_id,
            raw_data=can_frame.data_bytes
        )

        if frame_type == FrameType.SINGLE_FRAME:
            self._decode_single_frame(iso_frame, can_frame.data_bytes)
        elif frame_type == FrameType.FIRST_FRAME:
            self._decode_first_frame(iso_frame, can_frame.data_bytes)
        elif frame_type == FrameType.CONSECUTIVE_FRAME:
            self._decode_consecutive_frame(iso_frame, can_frame.data_bytes)
        elif frame_type == FrameType.FLOW_CONTROL:
            self._decode_flow_control_frame(iso_frame, can_frame.data_bytes)

        return iso_frame

    def _decode_single_frame(self, iso_frame: ISOTPFrame, data: List[int]):
        """Decode Single Frame (SF)"""
        if len(data) < 2:
            raise ValueError("Single frame too short")

        # SF_DL (Single Frame Data Length) is in lower 4 bits of PCI
        sf_length = data[0] & 0x0F

        if sf_length == 0:
            raise ValueError("Invalid single frame length: 0")

        if sf_length > 7:
            raise ValueError(f"Single frame length too large: {sf_length} (max 7)")

        if len(data) < sf_length + 1:
            raise ValueError(f"Single frame data too short for declared length {sf_length}")

        iso_frame.sf_length = sf_length
        iso_frame.sf_data = data[1:sf_length + 1]

    def _decode_first_frame(self, iso_frame: ISOTPFrame, data: List[int]):
        """Decode First Frame (FF)"""
        if len(data) < 2:
            raise ValueError("First frame too short")

        # FF_DL (First Frame Data Length) is 12 bits: lower 4 bits of PCI + next byte
        ff_length = ((data[0] & 0x0F) << 8) | data[1]

        if ff_length <= 7:
            raise ValueError(f"First frame length too small: {ff_length} (should be > 7)")

        if ff_length > 4095:
            raise ValueError(f"First frame length too large: {ff_length} (max 4095)")

        # First frame data starts from byte 2, up to 6 bytes in first frame
        ff_data = data[2:8]  # Take remaining bytes (up to 6)

        iso_frame.ff_length = ff_length
        iso_frame.ff_data = ff_data

    def _decode_consecutive_frame(self, iso_frame: ISOTPFrame, data: List[int]):
        """Decode Consecutive Frame (CF)"""
        if len(data) < 1:
            raise ValueError("Consecutive frame too short")

        # SN (Sequence Number) is in lower 4 bits of PCI
        sequence = data[0] & 0x0F

        # Consecutive frame data starts from byte 1, up to 7 bytes
        cf_data = data[1:8]  # Take remaining bytes (up to 7)

        iso_frame.cf_sequence = sequence
        iso_frame.cf_data = cf_data

    def _decode_flow_control_frame(self, iso_frame: ISOTPFrame, data: List[int]):
        """Decode Flow Control Frame (FC)"""
        if len(data) < 3:
            raise ValueError("Flow control frame too short")

        # FS (Flow Status) is in lower 4 bits of PCI
        flow_status_val = data[0] & 0x0F

        try:
            flow_status = FlowStatus(flow_status_val)
        except ValueError:
            raise ValueError(f"Invalid flow status: 0x{flow_status_val:X}")

        # BS (Block Size) - number of consecutive frames before next FC
        block_size = data[1]

        # STmin (Separation Time minimum) - minimum delay between frames
        separation_time = data[2]

        iso_frame.fc_status = flow_status
        iso_frame.fc_block_size = block_size
        iso_frame.fc_separation_time = separation_time

    def process_frame(self, can_frame: CANFrame) -> Optional[ISOTPMessage]:
        """
        Process a CAN frame and return a complete ISO-TP message if available

        Args:
            can_frame: CAN frame to process

        Returns:
            ISOTPMessage: Complete message if available, None if still assembling

        Raises:
            ValueError: If frame format is invalid or protocol error occurs
        """
        iso_frame = self.decode_frame(can_frame)

        if iso_frame.frame_type == FrameType.SINGLE_FRAME:
            # Single frame contains complete message
            return ISOTPMessage(
                can_id=iso_frame.can_id,
                data=iso_frame.sf_data,
                frame_count=1
            )

        elif iso_frame.frame_type == FrameType.FIRST_FRAME:
            # Start of multi-frame message
            self.pending_messages[iso_frame.can_id] = {
                'total_length': iso_frame.ff_length,
                'received_data': iso_frame.ff_data.copy(),
                'expected_sequence': 1,
                'frame_count': 1
            }
            return None  # Message not complete yet

        elif iso_frame.frame_type == FrameType.CONSECUTIVE_FRAME:
            # Continuation of multi-frame message
            if iso_frame.can_id not in self.pending_messages:
                raise ValueError(f"Consecutive frame without first frame for CAN ID 0x{iso_frame.can_id:03X}")

            msg_state = self.pending_messages[iso_frame.can_id]

            # Check sequence number
            if iso_frame.cf_sequence != msg_state['expected_sequence']:
                raise ValueError(f"Sequence error: expected {msg_state['expected_sequence']}, got {iso_frame.cf_sequence}")

            # Add data to message
            msg_state['received_data'].extend(iso_frame.cf_data)
            msg_state['frame_count'] += 1

            # Update expected sequence (wraps from 15 to 0)
            msg_state['expected_sequence'] = (msg_state['expected_sequence'] + 1) % 16

            # Check if message is complete
            if len(msg_state['received_data']) >= msg_state['total_length']:
                # Trim to exact length
                final_data = msg_state['received_data'][:msg_state['total_length']]
                frame_count = msg_state['frame_count']

                # Remove from pending
                del self.pending_messages[iso_frame.can_id]

                return ISOTPMessage(
                    can_id=iso_frame.can_id,
                    data=final_data,
                    frame_count=frame_count
                )

            return None  # Message not complete yet

        elif iso_frame.frame_type == FrameType.FLOW_CONTROL:
            # Flow control frame - just return the decoded frame info
            # In a real implementation, this would control transmission timing
            return None

        return None


def main():
    """Test the ISO-TP decoder"""
    from can_frame_parser import CANFrameParser

    parser = CANFrameParser()
    decoder = ISOTPDecoder()

    try:
        frames = parser.parse_file('transcript.txt')
        print(f"Processing {len(frames)} CAN frames...")
        print()

        messages = []

        for i, can_frame in enumerate(frames):
            try:
                message = decoder.process_frame(can_frame)
                if message:
                    messages.append(message)
                    print(f"Complete message {len(messages)}: {message}")
                else:
                    # Decode frame for display
                    iso_frame = decoder.decode_frame(can_frame)
                    print(f"Frame {i+1}: {_format_iso_frame(iso_frame)}")

            except ValueError as e:
                print(f"Error processing frame {i+1}: {e}")

        print(f"\nSummary: {len(messages)} complete ISO-TP messages decoded from {len(frames)} CAN frames")

    except FileNotFoundError:
        print("Error: transcript.txt file not found")
    except Exception as e:
        print(f"Error: {e}")


def _format_iso_frame(iso_frame: ISOTPFrame) -> str:
    """Format ISO-TP frame for display"""
    if iso_frame.frame_type == FrameType.SINGLE_FRAME:
        data_hex = ' '.join(f'{b:02X}' for b in iso_frame.sf_data)
        return f"SF [CAN ID: 0x{iso_frame.can_id:03X}, Length: {iso_frame.sf_length}]: {data_hex}"

    elif iso_frame.frame_type == FrameType.FIRST_FRAME:
        data_hex = ' '.join(f'{b:02X}' for b in iso_frame.ff_data)
        return f"FF [CAN ID: 0x{iso_frame.can_id:03X}, Total Length: {iso_frame.ff_length}]: {data_hex}..."

    elif iso_frame.frame_type == FrameType.CONSECUTIVE_FRAME:
        data_hex = ' '.join(f'{b:02X}' for b in iso_frame.cf_data)
        return f"CF [CAN ID: 0x{iso_frame.can_id:03X}, Seq: {iso_frame.cf_sequence}]: {data_hex}"

    elif iso_frame.frame_type == FrameType.FLOW_CONTROL:
        return f"FC [CAN ID: 0x{iso_frame.can_id:03X}, Status: {iso_frame.fc_status.name}, BS: {iso_frame.fc_block_size}, STmin: {iso_frame.fc_separation_time}]"

    return f"Unknown frame type: {iso_frame.frame_type}"


if __name__ == "__main__":
    main()