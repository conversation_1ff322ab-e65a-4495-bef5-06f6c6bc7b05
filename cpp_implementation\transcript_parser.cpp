#include "transcript_parser.h"
#include <iostream>
#include <sstream>
#include <iomanip>

namespace iso15765 {

// ParsingStatistics implementation
std::string ParsingStatistics::toString() const {
    std::ostringstream oss;
    oss << "Parsing Statistics:\n";
    oss << "------------------------------\n";
    oss << "Total CAN frames:      " << total_frames << "\n";
    oss << "Single frames:         " << single_frames << "\n";
    oss << "First frames:          " << first_frames << "\n";
    oss << "Consecutive frames:    " << consecutive_frames << "\n";
    oss << "Flow control frames:   " << flow_control_frames << "\n";
    oss << "Complete messages:     " << complete_messages << "\n";
    oss << "Parse errors:          " << parse_errors << "\n";

    if (total_frames > 0) {
        double efficiency = (static_cast<double>(complete_messages) / total_frames) * 100.0;
        oss << "Message efficiency:    " << std::fixed << std::setprecision(1) << efficiency << "%\n";
    }

    return oss.str();
}

// TranscriptParser implementation
TranscriptParser::TranscriptParser(bool verbose) : verbose_(verbose) {
    resetStatistics();
}

std::vector<std::unique_ptr<ISOTPMessage>> TranscriptParser::parseFile(const std::string& filename) {
    std::cout << "Parsing transcript file: " << filename << std::endl;
    std::cout << "============================================================" << std::endl;

    std::vector<std::unique_ptr<ISOTPMessage>> messages;

    try {
        // Parse CAN frames
        auto can_frames = can_parser_.parseFile(filename);
        stats_.total_frames = can_frames.size();

        if (can_frames.empty()) {
            std::cout << "No CAN frames found in file" << std::endl;
            return messages;
        }

        std::cout << "Found " << can_frames.size() << " CAN frames" << std::endl;
        std::cout << std::endl;

        // Process frames and decode ISO-TP messages
        for (size_t i = 0; i < can_frames.size(); ++i) {
            try {
                // Process frame
                auto message = iso_decoder_.processFrame(can_frames[i]);

                // Decode frame for statistics and verbose output
                ISOTPFrame iso_frame = iso_decoder_.decodeFrame(can_frames[i]);
                updateStats(iso_frame);

                if (verbose_) {
                    std::cout << "Frame " << std::setw(2) << (i + 1) << ": "
                              << formatFrameVerbose(can_frames[i], iso_frame) << std::endl;
                }

                if (message) {
                    messages.push_back(std::move(message));
                    std::cout << "Message " << std::setw(2) << messages.size() << ": "
                              << formatMessage(*messages.back()) << std::endl;
                    if (verbose_) {
                        std::cout << std::endl;
                    }
                }

            } catch (const std::exception& e) {
                stats_.parse_errors++;
                std::cout << "Error processing frame " << (i + 1) << ": " << e.what() << std::endl;
                if (verbose_) {
                    std::cout << "  Raw frame: " << can_frames[i].toString() << std::endl;
                    std::cout << std::endl;
                }
            }
        }

        stats_.complete_messages = messages.size();

    } catch (const std::exception& e) {
        std::cout << "Error reading file: " << e.what() << std::endl;
    }

    return messages;
}

void TranscriptParser::resetStatistics() {
    stats_ = ParsingStatistics{};
}

void TranscriptParser::updateStats(const ISOTPFrame& iso_frame) {
    switch (iso_frame.getFrameType()) {
        case FrameType::SINGLE_FRAME:
            stats_.single_frames++;
            break;
        case FrameType::FIRST_FRAME:
            stats_.first_frames++;
            break;
        case FrameType::CONSECUTIVE_FRAME:
            stats_.consecutive_frames++;
            break;
        case FrameType::FLOW_CONTROL:
            stats_.flow_control_frames++;
            break;
    }
}

std::string TranscriptParser::formatFrameVerbose(const CANFrame& can_frame, const ISOTPFrame& iso_frame) const {
    std::ostringstream oss;

    // Format raw CAN frame data
    oss << "CAN 0x" << std::hex << std::uppercase << std::setfill('0') << std::setw(3) << can_frame.getCanId() << " [";
    const auto& raw_data = can_frame.getDataBytes();
    for (size_t i = 0; i < raw_data.size(); ++i) {
        if (i > 0) oss << " ";
        oss << std::hex << std::uppercase << std::setfill('0') << std::setw(2) << static_cast<int>(raw_data[i]);
    }
    oss << "] -> ";

    // Format ISO-TP frame interpretation
    switch (iso_frame.getFrameType()) {
        case FrameType::SINGLE_FRAME: {
            auto sf_length = iso_frame.getSFLength();
            auto sf_data = iso_frame.getSFData();
            if (sf_length && sf_data) {
                oss << "SF(len=" << std::dec << static_cast<int>(*sf_length) << "): ";
                for (size_t i = 0; i < sf_data->size(); ++i) {
                    if (i > 0) oss << " ";
                    oss << std::hex << std::uppercase << std::setfill('0') << std::setw(2) << static_cast<int>((*sf_data)[i]);
                }
            }
            break;
        }

        case FrameType::FIRST_FRAME: {
            auto ff_length = iso_frame.getFFLength();
            auto ff_data = iso_frame.getFFData();
            if (ff_length && ff_data) {
                oss << "FF(total=" << std::dec << *ff_length << "): ";
                for (size_t i = 0; i < ff_data->size(); ++i) {
                    if (i > 0) oss << " ";
                    oss << std::hex << std::uppercase << std::setfill('0') << std::setw(2) << static_cast<int>((*ff_data)[i]);
                }
                oss << "...";
            }
            break;
        }

        case FrameType::CONSECUTIVE_FRAME: {
            auto cf_sequence = iso_frame.getCFSequence();
            auto cf_data = iso_frame.getCFData();
            if (cf_sequence && cf_data) {
                oss << "CF(seq=" << std::dec << static_cast<int>(*cf_sequence) << "): ";
                for (size_t i = 0; i < cf_data->size(); ++i) {
                    if (i > 0) oss << " ";
                    oss << std::hex << std::uppercase << std::setfill('0') << std::setw(2) << static_cast<int>((*cf_data)[i]);
                }
            }
            break;
        }

        case FrameType::FLOW_CONTROL: {
            auto fc_status = iso_frame.getFCStatus();
            auto fc_block_size = iso_frame.getFCBlockSize();
            auto fc_separation_time = iso_frame.getFCSeparationTime();
            if (fc_status && fc_block_size && fc_separation_time) {
                oss << "FC(status=";
                switch (*fc_status) {
                    case FlowStatus::CONTINUE_TO_SEND: oss << "CONTINUE_TO_SEND"; break;
                    case FlowStatus::WAIT: oss << "WAIT"; break;
                    case FlowStatus::OVERFLOW_ABORT: oss << "OVERFLOW_ABORT"; break;
                }
                oss << ", BS=" << std::dec << static_cast<int>(*fc_block_size)
                    << ", STmin=" << static_cast<int>(*fc_separation_time) << ")";
            }
            break;
        }
    }

    return oss.str();
}

std::string TranscriptParser::formatMessage(const ISOTPMessage& message) const {
    return message.toString();
}

} // namespace iso15765