#include "iso_tp_decoder.h"
#include <sstream>
#include <iomanip>

namespace iso15765 {

// ISOTPFrame implementation
ISOTPFrame::ISOTPFrame(FrameType frame_type, uint16_t can_id, const std::vector<uint8_t>& raw_data)
    : frame_type_(frame_type), can_id_(can_id), raw_data_(raw_data) {
}

void ISOTPFrame::setSingleFrameData(uint8_t length, const std::vector<uint8_t>& data) {
    sf_length_.emplace(length);
    sf_data_.emplace(data);
}

void ISOTPFrame::setFirstFrameData(uint16_t length, const std::vector<uint8_t>& data) {
    ff_length_.emplace(length);
    ff_data_.emplace(data);
}

void ISOTPFrame::setConsecutiveFrameData(uint8_t sequence, const std::vector<uint8_t>& data) {
    cf_sequence_.emplace(sequence);
    cf_data_.emplace(data);
}

void ISOTPFrame::setFlowControlData(FlowStatus status, uint8_t block_size, uint8_t separation_time) {
    fc_status_.emplace(status);
    fc_block_size_.emplace(block_size);
    fc_separation_time_.emplace(separation_time);
}

std::string ISOTPFrame::toString() const {
    std::ostringstream oss;

    switch (frame_type_) {
        case FrameType::SINGLE_FRAME:
            if (sf_length_ && sf_data_) {
                oss << "SF [CAN ID: 0x" << std::hex << std::uppercase << std::setfill('0') << std::setw(3) << can_id_
                    << ", Length: " << std::dec << static_cast<int>(*sf_length_) << "]: ";
                for (size_t i = 0; i < sf_data_->size(); ++i) {
                    if (i > 0) oss << " ";
                    oss << std::hex << std::uppercase << std::setfill('0') << std::setw(2) << static_cast<int>((*sf_data_)[i]);
                }
            }
            break;

        case FrameType::FIRST_FRAME:
            if (ff_length_ && ff_data_) {
                oss << "FF [CAN ID: 0x" << std::hex << std::uppercase << std::setfill('0') << std::setw(3) << can_id_
                    << ", Total Length: " << std::dec << *ff_length_ << "]: ";
                for (size_t i = 0; i < ff_data_->size(); ++i) {
                    if (i > 0) oss << " ";
                    oss << std::hex << std::uppercase << std::setfill('0') << std::setw(2) << static_cast<int>((*ff_data_)[i]);
                }
                oss << "...";
            }
            break;

        case FrameType::CONSECUTIVE_FRAME:
            if (cf_sequence_ && cf_data_) {
                oss << "CF [CAN ID: 0x" << std::hex << std::uppercase << std::setfill('0') << std::setw(3) << can_id_
                    << ", Seq: " << std::dec << static_cast<int>(*cf_sequence_) << "]: ";
                for (size_t i = 0; i < cf_data_->size(); ++i) {
                    if (i > 0) oss << " ";
                    oss << std::hex << std::uppercase << std::setfill('0') << std::setw(2) << static_cast<int>((*cf_data_)[i]);
                }
            }
            break;

        case FrameType::FLOW_CONTROL:
            if (fc_status_ && fc_block_size_ && fc_separation_time_) {
                oss << "FC [CAN ID: 0x" << std::hex << std::uppercase << std::setfill('0') << std::setw(3) << can_id_
                    << ", Status: ";
                switch (*fc_status_) {
                    case FlowStatus::CONTINUE_TO_SEND: oss << "CONTINUE_TO_SEND"; break;
                    case FlowStatus::WAIT: oss << "WAIT"; break;
                    case FlowStatus::OVERFLOW_ABORT: oss << "OVERFLOW_ABORT"; break;
                }
                oss << ", BS: " << std::dec << static_cast<int>(*fc_block_size_)
                    << ", STmin: " << static_cast<int>(*fc_separation_time_) << "]";
            }
            break;
    }

    return oss.str();
}

// ISOTPMessage implementation
ISOTPMessage::ISOTPMessage(uint16_t can_id, const std::vector<uint8_t>& data, size_t frame_count)
    : can_id_(can_id), data_(data), frame_count_(frame_count) {
}

std::string ISOTPMessage::toString() const {
    std::ostringstream oss;
    oss << "CAN ID 0x" << std::hex << std::uppercase << std::setfill('0') << std::setw(3) << can_id_
        << " (" << std::dec << frame_count_ << " frames, " << data_.size() << " bytes): ";

    for (size_t i = 0; i < data_.size(); ++i) {
        if (i > 0) oss << " ";
        oss << std::hex << std::uppercase << std::setfill('0') << std::setw(2) << static_cast<int>(data_[i]);
    }

    return oss.str();
}

// ISOTPDecoder implementation
ISOTPFrame ISOTPDecoder::decodeFrame(const CANFrame& can_frame) {
    const auto& data = can_frame.getDataBytes();

    if (data.empty()) {
        throw std::invalid_argument("Empty CAN frame data");
    }

    // Extract PCI (Protocol Control Information) from first byte
    uint8_t pci_byte = data[0];
    uint8_t frame_type_val = (pci_byte >> 4) & 0x0F;

    FrameType frame_type;
    switch (frame_type_val) {
        case 0x0: frame_type = FrameType::SINGLE_FRAME; break;
        case 0x1: frame_type = FrameType::FIRST_FRAME; break;
        case 0x2: frame_type = FrameType::CONSECUTIVE_FRAME; break;
        case 0x3: frame_type = FrameType::FLOW_CONTROL; break;
        default:
            throw std::invalid_argument("Invalid frame type: 0x" + std::to_string(frame_type_val));
    }

    ISOTPFrame iso_frame(frame_type, can_frame.getCanId(), data);

    switch (frame_type) {
        case FrameType::SINGLE_FRAME:
            decodeSingleFrame(iso_frame, data);
            break;
        case FrameType::FIRST_FRAME:
            decodeFirstFrame(iso_frame, data);
            break;
        case FrameType::CONSECUTIVE_FRAME:
            decodeConsecutiveFrame(iso_frame, data);
            break;
        case FrameType::FLOW_CONTROL:
            decodeFlowControlFrame(iso_frame, data);
            break;
    }

    return iso_frame;
}

std::unique_ptr<ISOTPMessage> ISOTPDecoder::processFrame(const CANFrame& can_frame) {
    ISOTPFrame iso_frame = decodeFrame(can_frame);

    switch (iso_frame.getFrameType()) {
        case FrameType::SINGLE_FRAME: {
            // Single frame contains complete message
            auto sf_data = iso_frame.getSFData();
            if (sf_data) {
                return std::make_unique<ISOTPMessage>(iso_frame.getCanId(), *sf_data, 1);
            }
            break;
        }

        case FrameType::FIRST_FRAME: {
            // Start of multi-frame message
            auto ff_length = iso_frame.getFFLength();
            auto ff_data = iso_frame.getFFData();
            if (ff_length && ff_data) {
                MessageState state;
                state.total_length = *ff_length;
                state.received_data = *ff_data;
                state.expected_sequence = 1;
                state.frame_count = 1;

                pending_messages_[iso_frame.getCanId()] = state;
            }
            break;
        }

        case FrameType::CONSECUTIVE_FRAME: {
            // Continuation of multi-frame message
            auto it = pending_messages_.find(iso_frame.getCanId());
            if (it == pending_messages_.end()) {
                throw std::invalid_argument("Consecutive frame without first frame for CAN ID 0x" +
                                          std::to_string(iso_frame.getCanId()));
            }

            auto cf_sequence = iso_frame.getCFSequence();
            auto cf_data = iso_frame.getCFData();
            if (!cf_sequence || !cf_data) {
                break;
            }

            MessageState& state = it->second;

            // Check sequence number
            if (*cf_sequence != state.expected_sequence) {
                throw std::invalid_argument("Sequence error: expected " +
                                          std::to_string(state.expected_sequence) +
                                          ", got " + std::to_string(*cf_sequence));
            }

            // Add data to message
            state.received_data.insert(state.received_data.end(), cf_data->begin(), cf_data->end());
            state.frame_count++;

            // Update expected sequence (wraps from 15 to 0)
            state.expected_sequence = (state.expected_sequence + 1) % 16;

            // Check if message is complete
            if (state.received_data.size() >= state.total_length) {
                // Trim to exact length
                std::vector<uint8_t> final_data(state.received_data.begin(),
                                               state.received_data.begin() + state.total_length);
                size_t frame_count = state.frame_count;
                uint16_t can_id = iso_frame.getCanId();

                // Remove from pending
                pending_messages_.erase(it);

                return std::make_unique<ISOTPMessage>(can_id, final_data, frame_count);
            }
            break;
        }

        case FrameType::FLOW_CONTROL:
            // Flow control frame - just acknowledge, no message to return
            break;
    }

    return nullptr;
}

void ISOTPDecoder::decodeSingleFrame(ISOTPFrame& iso_frame, const std::vector<uint8_t>& data) {
    if (data.size() < 2) {
        throw std::invalid_argument("Single frame too short");
    }

    // SF_DL (Single Frame Data Length) is in lower 4 bits of PCI
    uint8_t sf_length = data[0] & 0x0F;

    if (sf_length == 0) {
        throw std::invalid_argument("Invalid single frame length: 0");
    }

    if (sf_length > 7) {
        throw std::invalid_argument("Single frame length too large: " + std::to_string(sf_length) + " (max 7)");
    }

    if (data.size() < sf_length + 1) {
        throw std::invalid_argument("Single frame data too short for declared length " + std::to_string(sf_length));
    }

    std::vector<uint8_t> sf_data(data.begin() + 1, data.begin() + sf_length + 1);
    iso_frame.setSingleFrameData(sf_length, sf_data);
}

void ISOTPDecoder::decodeFirstFrame(ISOTPFrame& iso_frame, const std::vector<uint8_t>& data) {
    if (data.size() < 2) {
        throw std::invalid_argument("First frame too short");
    }

    // FF_DL (First Frame Data Length) is 12 bits: lower 4 bits of PCI + next byte
    uint16_t ff_length = ((data[0] & 0x0F) << 8) | data[1];

    if (ff_length <= 7) {
        throw std::invalid_argument("First frame length too small: " + std::to_string(ff_length) + " (should be > 7)");
    }

    if (ff_length > 4095) {
        throw std::invalid_argument("First frame length too large: " + std::to_string(ff_length) + " (max 4095)");
    }

    // First frame data starts from byte 2, up to 6 bytes in first frame
    std::vector<uint8_t> ff_data(data.begin() + 2, data.end());

    iso_frame.setFirstFrameData(ff_length, ff_data);
}

void ISOTPDecoder::decodeConsecutiveFrame(ISOTPFrame& iso_frame, const std::vector<uint8_t>& data) {
    if (data.empty()) {
        throw std::invalid_argument("Consecutive frame too short");
    }

    // SN (Sequence Number) is in lower 4 bits of PCI
    uint8_t sequence = data[0] & 0x0F;

    // Consecutive frame data starts from byte 1, up to 7 bytes
    std::vector<uint8_t> cf_data(data.begin() + 1, data.end());

    iso_frame.setConsecutiveFrameData(sequence, cf_data);
}

void ISOTPDecoder::decodeFlowControlFrame(ISOTPFrame& iso_frame, const std::vector<uint8_t>& data) {
    if (data.size() < 3) {
        throw std::invalid_argument("Flow control frame too short");
    }

    // FS (Flow Status) is in lower 4 bits of PCI
    uint8_t flow_status_val = data[0] & 0x0F;

    FlowStatus flow_status;
    switch (flow_status_val) {
        case 0x0: flow_status = FlowStatus::CONTINUE_TO_SEND; break;
        case 0x1: flow_status = FlowStatus::WAIT; break;
        case 0x2: flow_status = FlowStatus::OVERFLOW_ABORT; break;
        default:
            throw std::invalid_argument("Invalid flow status: 0x" + std::to_string(flow_status_val));
    }

    // BS (Block Size) - number of consecutive frames before next FC
    uint8_t block_size = data[1];

    // STmin (Separation Time minimum) - minimum delay between frames
    uint8_t separation_time = data[2];

    iso_frame.setFlowControlData(flow_status, block_size, separation_time);
}

} // namespace iso15765