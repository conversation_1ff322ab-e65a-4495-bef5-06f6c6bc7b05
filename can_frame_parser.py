#!/usr/bin/env python3
"""
CAN Frame Parser for ISO 15765-2 transcript data

This module parses CAN frames from the transcript.txt file format:
- 3 hex characters: CAN ID (11-bit)
- 16 hex characters: 8 bytes of data payload
"""

class CANFrame:
    """Represents a single CAN frame"""

    def __init__(self, can_id, data_bytes):
        self.can_id = can_id
        self.data_bytes = data_bytes
        self.dlc = len(data_bytes)  # Data Length Code

    def __str__(self):
        data_hex = ' '.join(f'{b:02X}' for b in self.data_bytes)
        return f"CAN ID: 0x{self.can_id:03X}, DLC: {self.dlc}, Data: [{data_hex}]"

    def __repr__(self):
        return f"CANFrame(can_id=0x{self.can_id:03X}, data_bytes={self.data_bytes})"


class CANFrameParser:
    """Parser for CAN frames from transcript format"""

    def parse_line(self, line):
        """
        Parse a single line from the transcript file

        Args:
            line (str): Line in format "XXXDDDDDDDDDDDDDDDD" where:
                       XXX = 3-char CAN ID
                       DDDDDDDDDDDDDDDD = 16-char (8 bytes) data

        Returns:
            CANFrame: Parsed CAN frame object

        Raises:
            ValueError: If line format is invalid
        """
        line = line.strip()

        if len(line) != 19:
            raise ValueError(f"Invalid line length: {len(line)}, expected 19 characters")

        try:
            # Extract CAN ID (first 3 hex characters)
            can_id_str = line[:3]
            can_id = int(can_id_str, 16)

            # Extract data bytes (remaining 16 hex characters = 8 bytes)
            data_str = line[3:]
            if len(data_str) != 16:
                raise ValueError(f"Invalid data length: {len(data_str)}, expected 16 characters")

            # Convert hex string to bytes
            data_bytes = []
            for i in range(0, 16, 2):
                byte_str = data_str[i:i+2]
                byte_val = int(byte_str, 16)
                data_bytes.append(byte_val)

            return CANFrame(can_id, data_bytes)

        except ValueError as e:
            raise ValueError(f"Failed to parse line '{line}': {e}")

    def parse_file(self, filename):
        """
        Parse all CAN frames from a transcript file

        Args:
            filename (str): Path to transcript file

        Returns:
            list[CANFrame]: List of parsed CAN frames
        """
        frames = []

        with open(filename, 'r') as f:
            for line_num, line in enumerate(f, 1):
                line = line.strip()
                if not line:  # Skip empty lines
                    continue

                try:
                    frame = self.parse_line(line)
                    frames.append(frame)
                except ValueError as e:
                    print(f"Warning: Error parsing line {line_num}: {e}")

        return frames


def main():
    """Test the CAN frame parser"""
    parser = CANFrameParser()

    try:
        frames = parser.parse_file('transcript.txt')
        print(f"Parsed {len(frames)} CAN frames:")
        print()

        for i, frame in enumerate(frames[:10]):  # Show first 10 frames
            print(f"Frame {i+1}: {frame}")

        if len(frames) > 10:
            print(f"... and {len(frames) - 10} more frames")

    except FileNotFoundError:
        print("Error: transcript.txt file not found")
    except Exception as e:
        print(f"Error: {e}")


if __name__ == "__main__":
    main()